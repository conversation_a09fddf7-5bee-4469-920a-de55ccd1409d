import 'package:flutter/material.dart';
import '../controllers/auth_controller.dart';
import '../database/database_helper.dart';
import '../models/khao_sat.dart';
import 'login_page.dart';
import 'thong_ke_page.dart';

class KhaoSatPage extends StatefulWidget {
  const KhaoSatPage({super.key});

  @override
  State<KhaoSatPage> createState() => _KhaoSatPageState();
}

class _KhaoSatPageState extends State<KhaoSatPage> {
  final AuthController _authController = AuthController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<KhaoSat> _parentSurveys = [];
  final Map<int, List<KhaoSat>> _childSurveys = {};
  final Map<int, bool> _expandedSurveys = {};
  final Map<int, bool> _selectedAnswers = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadKhaoSat();
  }

  Future<void> _loadKhaoSat() async {
    try {
      final allSurveys = await _databaseHelper.getAllKhaoSat();
      print('Tổng số khảo sát: ${allSurveys.length}');

      // In ra tất cả khảo sát để debug
      for (var survey in allSurveys) {
        print(
          'Khảo sát ID: ${survey.id}, Nội dung: ${survey.noiDungKhaoSat}, ID cha: ${survey.idKsCha}',
        );
      }

      // Phân loại khảo sát cha và con
      final parentSurveys = allSurveys.where((s) => s.idKsCha == null).toList();
      final childSurveys = <int, List<KhaoSat>>{};

      print('Số khảo sát cha: ${parentSurveys.length}');

      for (var parent in parentSurveys) {
        final children =
            allSurveys.where((s) => s.idKsCha == parent.id).toList();
        print('Khảo sát cha ${parent.id} có ${children.length} câu hỏi con');
        childSurveys[parent.id!] = children;
        _expandedSurveys[parent.id!] = false;

        // Khởi tạo trạng thái checkbox cho các câu hỏi con
        for (var child in children) {
          _selectedAnswers[child.id!] = false;
        }
      }

      setState(() {
        _parentSurveys = parentSurveys;
        _childSurveys.clear();
        _childSurveys.addAll(childSurveys);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Lỗi tải khảo sát: $e');
    }
  }

  Future<void> _logout() async {
    await _authController.logout();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );
    }
  }

  void _toggleExpansion(int surveyId) {
    setState(() {
      _expandedSurveys[surveyId] = !(_expandedSurveys[surveyId] ?? false);
    });
  }

  void _toggleAnswer(int questionId) {
    setState(() {
      _selectedAnswers[questionId] = !(_selectedAnswers[questionId] ?? false);
    });
  }

  Future<void> _submitSurvey() async {
    // Kiểm tra xem có câu hỏi nào được chọn không
    final selectedQuestions =
        _selectedAnswers.entries
            .where((entry) => entry.value == true)
            .map((entry) => entry.key)
            .toList();

    if (selectedQuestions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ít nhất một câu trả lời!'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Hiển thị dialog xác nhận
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Xác nhận bình chọn'),
          content: const Text('Bạn có chắc chắn muốn gửi kết quả bình chọn?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Xác nhận'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      // Hiển thị loading
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );
      }

      try {
        // Lưu kết quả bình chọn vào database
        final currentUser = _authController.currentUser;
        if (currentUser != null) {
          final success = await _databaseHelper.saveSurveyResults(
            currentUser.id!,
            _selectedAnswers,
          );

          if (mounted) {
            Navigator.of(context).pop(); // Đóng loading dialog

            if (success) {
              // Chuyển đến trang thống kê
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder:
                      (context) =>
                          ThongKePage(selectedAnswers: _selectedAnswers),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Có lỗi xảy ra khi lưu kết quả. Vui lòng thử lại!',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // Đóng loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Lỗi: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667eea), Color(0xFF764ba2), Color(0xFFf093fb)],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Custom App Bar với gradient
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.home, color: Colors.white),
                        onPressed: _logout,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Khảo Sát Ý Kiến',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              offset: const Offset(0, 2),
                              blurRadius: 4,
                              color: Colors.black.withOpacity(0.3),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child:
                    _isLoading
                        ? Center(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xFF667eea),
                                  ),
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Đang tải khảo sát...',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFF667eea),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        : _parentSurveys.isEmpty
                        ? Center(
                          child: Container(
                            padding: const EdgeInsets.all(30),
                            margin: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.poll_outlined,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Không có khảo sát nào',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        : Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView.builder(
                            padding: const EdgeInsets.only(bottom: 100),
                            itemCount: _parentSurveys.length,
                            itemBuilder: (context, index) {
                              final parentSurvey = _parentSurveys[index];
                              final children =
                                  _childSurveys[parentSurvey.id!] ?? [];
                              final isExpanded =
                                  _expandedSurveys[parentSurvey.id!] ?? false;

                              return _buildSurveyCard(
                                parentSurvey,
                                children,
                                isExpanded,
                                index,
                              );
                            },
                          ),
                        ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildSubmitButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildSurveyCard(
    KhaoSat parentSurvey,
    List<KhaoSat> children,
    bool isExpanded,
    int index,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Column(
            children: [
              // Header của khảo sát cha
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF667eea).withValues(alpha: 0.8),
                      const Color(0xFF764ba2).withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 8,
                  ),
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      Icons.quiz_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  title: Text(
                    parentSurvey.noiDungKhaoSat,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Colors.white,
                    ),
                  ),
                  subtitle: Text(
                    '${children.length} câu hỏi',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  ),
                  trailing: AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  onTap: () => _toggleExpansion(parentSurvey.id!),
                ),
              ),

              // Danh sách câu hỏi con với animation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: isExpanded ? null : 0,
                child:
                    isExpanded
                        ? Column(
                          children:
                              children.asMap().entries.map((entry) {
                                final childIndex = entry.key;
                                final child = entry.value;
                                final isSelected =
                                    _selectedAnswers[child.id!] ?? false;

                                return AnimatedContainer(
                                  duration: Duration(
                                    milliseconds: 200 + (childIndex * 50),
                                  ),
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isSelected
                                              ? const Color(
                                                0xFF667eea,
                                              ).withValues(alpha: 0.1)
                                              : Colors.grey.withValues(
                                                alpha: 0.05,
                                              ),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            isSelected
                                                ? const Color(0xFF667eea)
                                                : Colors.grey.withValues(
                                                  alpha: 0.2,
                                                ),
                                        width: isSelected ? 2 : 1,
                                      ),
                                    ),
                                    child: ListTile(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 4,
                                          ),
                                      leading: Transform.scale(
                                        scale: 1.2,
                                        child: Checkbox(
                                          value: isSelected,
                                          onChanged:
                                              (value) =>
                                                  _toggleAnswer(child.id!),
                                          activeColor: const Color(0xFF667eea),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                        ),
                                      ),
                                      title: Text(
                                        child.noiDungKhaoSat,
                                        style: TextStyle(
                                          fontSize: 15,
                                          fontWeight:
                                              isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w400,
                                          color:
                                              isSelected
                                                  ? const Color(0xFF667eea)
                                                  : Colors.grey[700],
                                        ),
                                      ),
                                      onTap: () => _toggleAnswer(child.id!),
                                    ),
                                  ),
                                );
                              }).toList(),
                        )
                        : const SizedBox.shrink(),
              ),

              if (isExpanded) const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    final hasSelectedAnswers = _selectedAnswers.values.any(
      (selected) => selected,
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: hasSelectedAnswers ? 200 : 160,
      height: 56,
      child: Container(
        decoration: BoxDecoration(
          gradient:
              hasSelectedAnswers
                  ? const LinearGradient(
                    colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  )
                  : LinearGradient(
                    colors: [
                      Colors.grey.withValues(alpha: 0.6),
                      Colors.grey.withValues(alpha: 0.8),
                    ],
                  ),
          borderRadius: BorderRadius.circular(28),
          boxShadow:
              hasSelectedAnswers
                  ? [
                    BoxShadow(
                      color: const Color(0xFF667eea).withValues(alpha: 0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ]
                  : [],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(28),
            onTap: hasSelectedAnswers ? _submitSurvey : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    hasSelectedAnswers ? Icons.how_to_vote : Icons.block,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      hasSelectedAnswers ? 'Bình Chọn' : 'Chọn câu trả lời',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
