# Ứng dụng Quản lý <PERSON>hảo sát Cá nhân

Ứng dụng Flutter được xây dựng theo mô hình MVC để quản lý khảo sát cá nhân với cơ sở dữ liệu SQLite.

## Cấu trúc Database

### Bảng User
- `id` (INTEGER PRIMARY KEY): ID người dùng
- `matKhau` (TEXT): M<PERSON><PERSON> khẩu
- `hoTen` (TEXT): Họ và tên
- `gioiTinh` (TEXT): Gi<PERSON><PERSON> tính
- `hinhMinhHoa` (TEXT): Đường dẫn hình ảnh (có thể null)

### Bảng KhaoSat
- `id` (INTEGER PRIMARY KEY): ID khảo sát
- `noiDungKhaoSat` (TEXT): Nội dung khảo sát
- `idKsCha` (INTEGER): ID khảo sát cha (c<PERSON> thể null)

### Bảng ThucHienKS
- `id` (INTEGER PRIMARY KEY): ID thực hiện khảo sát
- `ngay` (INTEGER): <PERSON><PERSON><PERSON> thực hiện (timestamp)
- `idKhaoSat` (INTEGER): ID khảo sát
- `idUser` (INTEGER): ID người dùng

### Bảng CTThucHienKS
- `id` (INTEGER PRIMARY KEY): ID chi tiết thực hiện
- `idKs` (INTEGER): ID khảo sát
- `binhChon` (TEXT): Lựa chọn/bình chọn

## Tính năng chính

### 1. Trang Đăng nhập
- Nhập ID người dùng (số nguyên) và mật khẩu
- Checkbox "Ghi nhớ mật khẩu" để lưu thông tin đăng nhập
- Nút hiện/ẩn mật khẩu
- Tự động điền thông tin nếu đã đăng nhập trước đó
- Hiển thị thông báo lỗi khi đăng nhập thất bại

### 2. Trang Khảo sát
- Hiển thị thông tin người dùng đã đăng nhập
- Danh sách các khảo sát có sẵn
- Chức năng đăng xuất



## Dữ liệu mẫu

Ứng dụng đã có sẵn 2 tài khoản để test:

### Tài khoản 1:
- **ID**: 1
- **Mật khẩu**: 123456
- **Họ tên**: Lê Đức Trung
- **Giới tính**: Nam

### Tài khoản 2:
- **ID**: 2
- **Mật khẩu**: minh2004
- **Họ tên**: Trần Công Minh
- **Giới tính**: Nữ

## Cấu trúc thư mục

```
lib/
├── main.dart                 # Entry point của ứng dụng
├── models/                   # Các model classes
│   ├── user.dart
│   ├── khao_sat.dart
│   ├── thuc_hien_ks.dart
│   └── ct_thuc_hien_ks.dart
├── views/                    # Các trang giao diện
│   ├── login_page.dart
│   └── khao_sat_page.dart
├── controllers/              # Các controller xử lý logic
│   └── auth_controller.dart
└── database/                 # Quản lý cơ sở dữ liệu
    └── database_helper.dart
```

## Cách chạy ứng dụng

1. Đảm bảo đã cài đặt Flutter SDK
2. Chạy lệnh để cài đặt dependencies:
   ```bash
   flutter pub get
   ```
3. Chạy ứng dụng:
   ```bash
   flutter run
   ```

## Dependencies sử dụng

- `sqflite`: Để làm việc với SQLite database
- `shared_preferences`: Để lưu trữ thông tin đăng nhập
- `crypto`: Để mã hóa mật khẩu (nếu cần)
- `path`: Để xử lý đường dẫn file database

## Hướng dẫn sử dụng

1. **Khởi động ứng dụng**: Ứng dụng sẽ hiển thị splash screen và tự động kiểm tra thông tin đăng nhập đã lưu
2. **Đăng nhập**: Nhập ID và mật khẩu, có thể chọn "Ghi nhớ mật khẩu"
3. **Xem khảo sát**: Sau khi đăng nhập thành công, xem danh sách khảo sát
4. **Đăng xuất**: Nhấn nút đăng xuất ở góc phải trên cùng
