import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/user.dart';
import '../models/khao_sat.dart';
import '../models/thuc_hien_ks.dart';
import '../models/ct_thuc_hien_ks.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'khao_sat.db');
    return await openDatabase(
      path,
      version: 5, // Tăng version để force recreate
      onCreate: _onCreate,
      onUpgrade: (db, oldVersion, newVersion) async {
        // Drop all tables and recreate
        await db.execute('DROP TABLE IF EXISTS CTThucHienKS');
        await db.execute('DROP TABLE IF EXISTS ThucHienKS');
        await db.execute('DROP TABLE IF EXISTS KhaoSat');
        await db.execute('DROP TABLE IF EXISTS User');
        await _onCreate(db, newVersion);
      },
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Tạo bảng User
    await db.execute('''
      CREATE TABLE User (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        matKhau TEXT NOT NULL,
        hoTen TEXT NOT NULL,
        gioiTinh TEXT NOT NULL,
        hinhMinhHoa TEXT
      )
    ''');

    // Tạo bảng KhaoSat
    await db.execute('''
      CREATE TABLE KhaoSat (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        noiDungKhaoSat TEXT NOT NULL,
        idKsCha INTEGER,
        FOREIGN KEY (idKsCha) REFERENCES KhaoSat (id)
      )
    ''');

    // Tạo bảng ThucHienKS
    await db.execute('''
      CREATE TABLE ThucHienKS (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ngay INTEGER NOT NULL,
        idKhaoSat INTEGER NOT NULL,
        idUser INTEGER NOT NULL,
        FOREIGN KEY (idKhaoSat) REFERENCES KhaoSat (id),
        FOREIGN KEY (idUser) REFERENCES User (id)
      )
    ''');

    // Tạo bảng CTThucHienKS
    await db.execute('''
      CREATE TABLE CTThucHienKS (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        idKs INTEGER NOT NULL,
        binhChon TEXT NOT NULL,
        FOREIGN KEY (idKs) REFERENCES KhaoSat (id)
      )
    ''');

    // Thêm dữ liệu mẫu
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    print('Đang thêm dữ liệu mẫu...');
    // Thêm user mẫu
    await db.insert('User', {
      'id': 1,
      'matKhau': '123456',
      'hoTen': 'Lê Đức Trung',
      'gioiTinh': 'Nam',
      'hinhMinhHoa': null,
    });

    await db.insert('User', {
      'id': 2,
      'matKhau': 'minh2004',
      'hoTen': 'Trần Công Minh',
      'gioiTinh': 'Nữ',
      'hinhMinhHoa': null,
    });

    // Thêm khảo sát cha
    await db.insert('KhaoSat', {
      'id': 1,
      'noiDungKhaoSat': 'Khảo sát về sức khỏe cá nhân',
      'idKsCha': null,
    });

    await db.insert('KhaoSat', {
      'id': 2,
      'noiDungKhaoSat': 'Khảo sát về chế độ dinh dưỡng',
      'idKsCha': null,
    });

    // Thêm khảo sát con cho "Khảo sát về sức khỏe cá nhân"
    await db.insert('KhaoSat', {
      'id': 3,
      'noiDungKhaoSat': 'Bạn có thể cảm thấy mình khỏe mạnh?',
      'idKsCha': 1,
    });

    await db.insert('KhaoSat', {
      'id': 4,
      'noiDungKhaoSat': 'Bạn có ngủ đủ 7-8 tiếng mỗi ngày không?',
      'idKsCha': 1,
    });

    await db.insert('KhaoSat', {
      'id': 5,
      'noiDungKhaoSat': 'Bạn có tập thể dục khỏe định kỳ không?',
      'idKsCha': 1,
    });

    // Thêm khảo sát con cho "Khảo sát về chế độ dinh dưỡng"
    await db.insert('KhaoSat', {
      'id': 6,
      'noiDungKhaoSat': 'Bạn có ăn đủ rau và trái cây mỗi ngày không?',
      'idKsCha': 2,
    });

    await db.insert('KhaoSat', {
      'id': 7,
      'noiDungKhaoSat': 'Bạn có thường xuyên ăn thức ăn nhanh không?',
      'idKsCha': 2,
    });

    await db.insert('KhaoSat', {
      'id': 8,
      'noiDungKhaoSat': 'Bạn có uống đủ nước mỗi ngày không?',
      'idKsCha': 2,
    });

    print('Đã thêm xong dữ liệu mẫu!');

    // Thêm một số dữ liệu thống kê mẫu
    await _insertSampleStatistics(db);
  }

  Future<void> _insertSampleStatistics(Database db) async {
    print('Đang thêm dữ liệu thống kê mẫu...');

    // Thêm một số lượt thực hiện khảo sát mẫu
    await db.insert('ThucHienKS', {
      'id': 1,
      'ngay': DateTime.now().millisecondsSinceEpoch,
      'idKhaoSat': 1,
      'idUser': 1,
    });

    await db.insert('ThucHienKS', {
      'id': 2,
      'ngay': DateTime.now().millisecondsSinceEpoch,
      'idKhaoSat': 1,
      'idUser': 2,
    });

    // Thêm chi tiết bình chọn mẫu
    // User 1 chọn câu 3, 4, 6, 7
    await db.insert('CTThucHienKS', {'idKs': 3, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 4, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 6, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 7, 'binhChon': 'yes'});

    // User 2 chọn câu 3, 5, 6, 8
    await db.insert('CTThucHienKS', {'idKs': 3, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 5, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 6, 'binhChon': 'yes'});
    await db.insert('CTThucHienKS', {'idKs': 8, 'binhChon': 'yes'});

    print('Đã thêm xong dữ liệu thống kê mẫu!');
  }

  // CRUD operations cho User
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('User', user.toMap());
  }

  Future<User?> getUserById(int id) async {
    final db = await database;
    final maps = await db.query('User', where: 'id = ?', whereArgs: [id]);
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> authenticateUser(int id, String password) async {
    final db = await database;
    final maps = await db.query(
      'User',
      where: 'id = ? AND matKhau = ?',
      whereArgs: [id, password],
    );
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final maps = await db.query('User');
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  // CRUD operations cho KhaoSat
  Future<int> insertKhaoSat(KhaoSat khaoSat) async {
    final db = await database;
    return await db.insert('KhaoSat', khaoSat.toMap());
  }

  Future<List<KhaoSat>> getAllKhaoSat() async {
    final db = await database;
    final maps = await db.query('KhaoSat');
    return List.generate(maps.length, (i) => KhaoSat.fromMap(maps[i]));
  }

  // CRUD operations cho ThucHienKS
  Future<int> insertThucHienKS(ThucHienKS thucHienKS) async {
    final db = await database;
    return await db.insert('ThucHienKS', thucHienKS.toMap());
  }

  // CRUD operations cho CTThucHienKS
  Future<int> insertCTThucHienKS(CTThucHienKS ctThucHienKS) async {
    final db = await database;
    return await db.insert('CTThucHienKS', ctThucHienKS.toMap());
  }

  // Lưu kết quả bình chọn
  Future<bool> saveSurveyResults(
    int userId,
    Map<int, bool> selectedAnswers,
  ) async {
    final db = await database;

    try {
      // Bắt đầu transaction
      await db.transaction((txn) async {
        // Lưu thông tin thực hiện khảo sát
        final thucHienKS = ThucHienKS(
          ngay: DateTime.now(),
          idKhaoSat: 1, // Có thể điều chỉnh theo khảo sát cụ thể
          idUser: userId,
        );

        final thucHienId = await txn.insert('ThucHienKS', thucHienKS.toMap());

        // Lưu chi tiết các câu trả lời được chọn
        for (var entry in selectedAnswers.entries) {
          if (entry.value == true) {
            // Chỉ lưu những câu được chọn
            final ctThucHienKS = CTThucHienKS(
              idKs: entry.key,
              binhChon: 'yes', // Đánh dấu là đã chọn
            );

            await txn.insert('CTThucHienKS', ctThucHienKS.toMap());
          }
        }
      });

      return true;
    } catch (e) {
      print('Lỗi lưu kết quả khảo sát: $e');
      return false;
    }
  }

  // Lấy thống kê tổng số lượt bình chọn cho mỗi câu hỏi
  Future<Map<int, int>> getSurveyStatistics() async {
    final db = await database;

    try {
      final result = await db.rawQuery('''
        SELECT idKs, COUNT(*) as count
        FROM CTThucHienKS
        WHERE binhChon = 'yes'
        GROUP BY idKs
      ''');

      final statistics = <int, int>{};
      for (var row in result) {
        statistics[row['idKs'] as int] = row['count'] as int;
      }

      return statistics;
    } catch (e) {
      print('Lỗi lấy thống kê: $e');
      return {};
    }
  }

  // Kiểm tra user đã tham gia khảo sát chưa
  Future<bool> hasUserParticipated(int userId) async {
    final db = await database;

    try {
      final result = await db.query(
        'ThucHienKS',
        where: 'idUser = ?',
        whereArgs: [userId],
      );

      return result.isNotEmpty;
    } catch (e) {
      print('Lỗi kiểm tra tham gia: $e');
      return false;
    }
  }

  // Lấy tổng số người đã tham gia khảo sát
  Future<int> getTotalParticipants() async {
    final db = await database;

    try {
      final result = await db.rawQuery('''
        SELECT COUNT(DISTINCT idUser) as count
        FROM ThucHienKS
      ''');

      return result.first['count'] as int;
    } catch (e) {
      print('Lỗi lấy số người tham gia: $e');
      return 0;
    }
  }
}
